{"repos": [{"name": "terraform-google-appengine", "pipelines": ["terraform-google-appengine_tftest_RPD_01_default", "terraform-google-appengine_tftest_STT_01_default", "terraform-google-appengine_checkov"]}, {"name": "terraform-google-artifact-registry", "pipelines": ["terraform-google-artifact-registry_tftest_RPD_01-default", "terraform-google-artifact-registry_tftest_STT_01-default", "terraform-google-artifact-registry_checkov"]}, {"name": "terraform-google-bigquery", "pipelines": ["terraform-google-bigquery_tftest_RPD_01_default", "terraform-google-bigquery_tftest_STT_01_default", "terraform-google-big<PERSON><PERSON>_checkov"]}, {"name": "terraform-google-cloud-function", "pipelines": ["terraform-google-cloud-function-tftest_STT_01-default", "terraform-google-cloud-function-tftest_RPD_01-default", "terraform-google-cloud-function-checkov"]}, {"name": "terraform-google-cloud-run", "pipelines": ["terraform-google-cloud-run_tftest_STT_01-default", "terraform-google-cloud-run_tftest_RPD_01-default", "terraform-google-cloud-run_checkov"]}, {"name": "terraform-google-composer", "pipelines": ["terraform-google-composer_tftest_RPD_01-default", "terraform-google-composer_tftest_STT_01-default", "terraform-google-composer_checkov"]}, {"name": "terraform-google-compute-address", "pipelines": ["terraform-google-compute-address_tftest_STT_01-default", "terraform-google-compute-address_tftest_RPD_01-default", "terraform-google-compute-address_checkov"]}, {"name": "terraform-google-compute-disk", "pipelines": ["terraform-google-compute-disk_tftest_RPD_01-default", "terraform-google-compute-disk_tftest_STT_01-default", "terraform-google-compute-disk_checkov"]}, {"name": "terraform-google-compute-instance", "pipelines": ["terraform-google-compute-instance-tftest_RPD_01-default", "terraform-google-compute-instance-tftest_STT_01-default", "terraform-google-compute-instance-checkov"]}, {"name": "terraform-google-compute-instance-template", "pipelines": ["terraform-google-compute-instance-template_checkov"]}, {"name": "terraform-google-eventarc", "pipelines": ["terraform-google-eventarc_tftest_STT_01-default", "terraform-google-eventarc_tftest_RPD_01-default", "terraform-google-eventarc_checkov"]}, {"name": "terraform-google-firebase", "pipelines": ["terraform-google-firebase_tftest_RPD_01-default", "terraform-google-firebase_tftest_STT_01-default", "terraform-google-firebase_checkov"]}, {"name": "terraform-google-firestore", "pipelines": ["terraform-google-firestore_tftest_RPD_01-default", "terraform-google-firestore_tftest_STT_01-default", "terraform-google-firestore_checkov"]}, {"name": "terraform-google-iam", "pipelines": ["terraform-google-iam_tftest_STT_01-default", "terraform-google-iam_tftest_RPD_01-default", "terraform-google-iam_tftest_STT_02-bindings", "terraform-google-iam_tftest_RPD_02-bindings", "terraform-google-iam_checkov"]}, {"name": "terraform-google-managed-instance-group", "pipelines": ["terraform-google-managed-instance-group_tftest_RPD_01-default", "terraform-google-managed-instance-group_tftest_STT_01-default", "terraform-google-managed-instance-group_checkov"]}, {"name": "terraform-google-network-vpc", "pipelines": ["terraform-google-network-vpc_tftest_RPD_01-default", "terraform-google-network-vpc_tftest_STT_01-default", "terraform-google-network-vpc_tftest_RPD_02-new", "terraform-google-network-vpc_tftest_STT_02-new", "terraform-google-network-vpc_tftest_STT_03-peering", "terraform-google-network-vpc_tftest_RPD_03-peering", "terraform-google-network-vpc_checkov"]}, {"name": "terraform-google-policy", "pipelines": ["terraform-google-policy_checkov"]}, {"name": "terraform-google-project", "pipelines": ["terraform-google-project_checkov"]}, {"name": "terraform-google-pubsub", "pipelines": ["terraform-google-pubsub_tftest-STT-01-default", "terraform-google-pubsub_tftest-RPD-01-default", "terraform-google-pubsub_checkov"]}, {"name": "terraform-google-secret-manager", "pipelines": ["terraform-google-secret-manager_tftest_RPD_01-default", "terraform-google-secret-manager_tftest_STT_01-default", "terraform-google-secret-manager_checkov"]}, {"name": "terraform-google-storage-bucket", "pipelines": ["terraform-google-storage-bucket_tftest_STT_01-default", "terraform-google-storage-bucket_tftest_RPD_01-default", "terraform-google-storage-bucket_tftest_TST_01-default", "terraform-google-storage-bucket_checkov"]}, {"name": "terraform-google-vertexai", "pipelines": ["terraform-google-vertexai_tftest_STT_01-default", "terraform-google-vertexai_tftest_RPD_01-default", "terraform-google-<PERSON><PERSON>_checkov"]}, {"name": "terraform-google-workflows", "pipelines": ["terraform-google-workflows_tftest_RPD_01-default", "terraform-google-workflows_tftest_STT_01-default", "terraform-google-workflows_checkov"]}]}